#!/usr/bin/env python3
"""
Test script for the new crop centering and padded preview functionality.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def create_test_image():
    """Create a test image with a person-like shape."""
    # Create a 800x600 image
    image = np.zeros((600, 800, 3), dtype=np.uint8)
    
    # Add a background gradient
    for y in range(600):
        for x in range(800):
            image[y, x] = [50 + (x // 10), 100 + (y // 10), 150]
    
    # Add a person-like shape (off-center to test centering)
    # Head (circle)
    cv2.circle(image, (300, 150), 40, (220, 180, 160), -1)
    
    # Body (rectangle)
    cv2.rectangle(image, (260, 190), (340, 350), (100, 150, 200), -1)
    
    # Arms
    cv2.rectangle(image, (200, 200), (260, 240), (100, 150, 200), -1)
    cv2.rectangle(image, (340, 200), (400, 240), (100, 150, 200), -1)
    
    # Legs
    cv2.rectangle(image, (270, 350), (300, 450), (80, 120, 180), -1)
    cv2.rectangle(image, (310, 350), (340, 450), (80, 120, 180), -1)
    
    return image

def create_mock_detection(image):
    """Create mock detection data for the test image."""
    height, width = image.shape[:2]
    
    # Mock bounding box around the person shape
    bbox = [200, 100, 400, 450]  # [x1, y1, x2, y2]
    
    # Mock keypoints
    keypoints = {
        'nose': (300, 150),
        'left_eye': (290, 140),
        'right_eye': (310, 140),
        'left_shoulder': (280, 200),
        'right_shoulder': (320, 200),
        'left_hip': (285, 340),
        'right_hip': (315, 340)
    }
    
    detection = {
        'bbox': bbox,
        'confidence': 0.95,
        'keypoints': keypoints,
        'center': ((bbox[0] + bbox[2]) // 2, (bbox[1] + bbox[3]) // 2)
    }
    
    return detection

def test_crop_centering():
    """Test the crop centering functionality."""
    print("Testing crop centering functionality...")
    
    try:
        from src.photo_center.image_processing.crop_centering import CropCenterer
        from src.photo_center.image_processing.padded_preview import PaddedPreviewGenerator
        from src.photo_center.utils.config import Config
        
        # Create test image and detection
        test_image = create_test_image()
        detection = create_mock_detection(test_image)
        
        print(f"Test image size: {test_image.shape[1]}x{test_image.shape[0]}")
        print(f"Subject center (bbox): {detection['center']}")
        
        # Initialize crop centerer
        config = Config()
        crop_centerer = CropCenterer(config)
        
        # Test different centering methods
        methods = ['face_chest_based', 'keypoint_based', 'bbox_based']
        
        for method in methods:
            print(f"\nTesting {method} centering:")
            
            # Perform crop centering
            crop_result = crop_centerer.center_subject_with_crop(
                test_image, detection, centering_method=method
            )
            
            print(f"  Crop box: {crop_result.crop_box}")
            print(f"  Crop ratio: {crop_result.crop_ratio:.3f}")
            print(f"  Confidence: {crop_result.confidence:.3f}")
            print(f"  Padding needed: {crop_result.padding_needed}")
            
            # Test padded preview generation
            preview_gen = PaddedPreviewGenerator()
            orig_height, orig_width = test_image.shape[:2]
            
            # Create padded preview
            padded_result = preview_gen.create_padded_preview(
                crop_result, (orig_width, orig_height), maintain_aspect=True
            )
            
            print(f"  Padded preview size: {padded_result.preview_image.shape[1]}x{padded_result.preview_image.shape[0]}")
            print(f"  Scale factor: {padded_result.scale_factor:.3f}")
            print(f"  Padding applied: {padded_result.padding_applied}")
            
            # Save test images
            output_dir = Path("test_output")
            output_dir.mkdir(exist_ok=True)
            
            # Save cropped image
            cv2.imwrite(str(output_dir / f"cropped_{method}.jpg"), crop_result.cropped_image)
            
            # Save padded preview
            cv2.imwrite(str(output_dir / f"padded_{method}.jpg"), padded_result.preview_image)
            
            # Save overlay preview
            overlay_image = preview_gen.create_overlay_preview(test_image, crop_result)
            cv2.imwrite(str(output_dir / f"overlay_{method}.jpg"), overlay_image)
            
            # Save side-by-side preview
            side_by_side = preview_gen.create_side_by_side_preview(test_image, crop_result)
            cv2.imwrite(str(output_dir / f"comparison_{method}.jpg"), side_by_side)
        
        # Save original test image for reference
        cv2.imwrite(str(output_dir / "original_test.jpg"), test_image)
        
        print(f"\n✓ Crop centering test completed successfully!")
        print(f"Test images saved to: {output_dir.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Crop centering test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_perfect_centering():
    """Test perfect left-right centering with different subject positions."""
    print("\nTesting perfect left-right centering...")
    
    try:
        from src.photo_center.image_processing.crop_centering import CropCenterer
        from src.photo_center.utils.config import Config
        
        # Create images with subjects at different positions
        positions = [
            (200, 300),  # Left side
            (400, 300),  # Center
            (600, 300),  # Right side
        ]
        
        config = Config()
        crop_centerer = CropCenterer(config)
        
        for i, (subject_x, subject_y) in enumerate(positions):
            # Create test image
            image = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # Add background
            image[:] = [100, 150, 200]
            
            # Add subject at specific position
            cv2.circle(image, (subject_x, subject_y), 50, (255, 255, 255), -1)
            cv2.rectangle(image, (subject_x - 30, subject_y + 20), (subject_x + 30, subject_y + 120), (200, 200, 200), -1)
            
            # Create detection
            detection = {
                'bbox': [subject_x - 60, subject_y - 60, subject_x + 60, subject_y + 140],
                'confidence': 0.9,
                'keypoints': {'nose': (subject_x, subject_y)},
                'center': (subject_x, subject_y)
            }
            
            # Test crop centering
            crop_result = crop_centerer.center_subject_with_crop(
                image, detection, centering_method='bbox_based'
            )
            
            print(f"Position {i+1} - Subject at ({subject_x}, {subject_y}):")
            print(f"  Original subject position: {subject_x} (from left edge)")
            print(f"  Crop box: {crop_result.crop_box}")
            
            # Calculate where subject ends up in cropped image
            crop_x1, crop_y1, crop_x2, crop_y2 = crop_result.crop_box
            cropped_width = crop_x2 - crop_x1
            subject_in_crop = subject_x - crop_x1
            center_position = cropped_width // 2
            
            print(f"  Subject in cropped image: {subject_in_crop} (center should be {center_position})")
            print(f"  Centering error: {abs(subject_in_crop - center_position)} pixels")
            print(f"  Crop ratio: {crop_result.crop_ratio:.3f}")
            
            # Save test image
            output_dir = Path("test_output")
            output_dir.mkdir(exist_ok=True)
            cv2.imwrite(str(output_dir / f"position_test_{i+1}_original.jpg"), image)
            cv2.imwrite(str(output_dir / f"position_test_{i+1}_cropped.jpg"), crop_result.cropped_image)
        
        print("✓ Perfect centering test completed!")
        return True
        
    except Exception as e:
        print(f"✗ Perfect centering test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all crop centering tests."""
    print("Crop Centering and Padded Preview Test")
    print("=" * 50)
    
    tests = [
        test_crop_centering,
        test_perfect_centering,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user")
            break
        except Exception as e:
            print(f"\n✗ Unexpected error in {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed!")
        print("\nCrop centering functionality is working correctly!")
        print("Check the 'test_output' directory for generated test images.")
        return 0
    else:
        print(f"✗ {total - passed} out of {total} tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
