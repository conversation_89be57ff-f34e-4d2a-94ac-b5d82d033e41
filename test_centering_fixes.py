#!/usr/bin/env python3
"""
Test script to verify the centering improvements work correctly.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def create_test_image_with_person():
    """Create a test image with a simple person-like shape."""
    # Create a 800x600 image
    image = np.zeros((600, 800, 3), dtype=np.uint8)
    
    # Add some background
    image[:] = (50, 50, 50)  # Dark gray background
    
    # Draw a simple person shape
    # Head (circle)
    cv2.circle(image, (300, 150), 40, (200, 180, 160), -1)
    
    # Body (rectangle)
    cv2.rectangle(image, (260, 190), (340, 350), (100, 120, 140), -1)
    
    # Arms
    cv2.rectangle(image, (200, 200), (260, 230), (100, 120, 140), -1)  # Left arm
    cv2.rectangle(image, (340, 200), (400, 230), (100, 120, 140), -1)  # Right arm
    
    # Legs
    cv2.rectangle(image, (270, 350), (300, 450), (80, 100, 120), -1)   # Left leg
    cv2.rectangle(image, (300, 350), (330, 450), (80, 100, 120), -1)   # Right leg
    
    return image

def test_centering_logic():
    """Test the centering logic with debug output."""
    print("Testing centering improvements...")

    try:
        from src.photo_center.models.human_detector import HumanDetector
        from src.photo_center.image_processing.centering import PhotoCenterer
        from src.photo_center.utils.config import Config
        from src.photo_center.image_processing.raw_processor import RawProcessor

        # Use a real test image instead of synthetic
        test_image_path = "test_photos/pexels-reneterp-641314.jpg"
        if not Path(test_image_path).exists():
            print(f"Test image not found: {test_image_path}")
            print("Please ensure test image exists")
            return False

        # Load real image
        raw_processor = RawProcessor()
        test_image = raw_processor.load_image(test_image_path)
        print(f"Loaded test image: {test_image.shape}")

        # Initialize components
        config = Config()
        detector = HumanDetector(config)
        centerer = PhotoCenterer(config)

        print(f"Target position from config: {config.target_position}")
        print(f"Margin ratio: {config.margin_ratio}")

        # Detect humans
        print("Detecting humans...")
        detections = detector.detect_humans(test_image)
        print(f"Found {len(detections)} detections")
        
        if detections:
            best_detection = detector.get_best_detection(detections)
            print(f"Best detection confidence: {best_detection['confidence']:.3f}")
            print(f"Bounding box: {best_detection['bbox']}")
            print(f"Has keypoints: {best_detection.get('keypoints') is not None}")
            
            # Test centering
            print("\nTesting centering...")
            result = centerer.center_subject(test_image, best_detection)
            
            print(f"Centering method used: {result.method_used}")
            print(f"Centering confidence: {result.confidence:.3f}")
            print(f"Subject center: {result.subject_center}")
            print(f"Target center: {result.target_center}")
            print(f"Crop box: {result.crop_box}")
            print(f"Cropped image size: {result.cropped_image.shape}")
            
            # Test visualizations
            print("\nTesting visualizations...")
            
            # Test detection visualization
            detection_vis = detector.visualize_detections(test_image, detections)
            print(f"Detection visualization created: {detection_vis.shape}")
            
            # Test centering visualization
            centering_vis = centerer.visualize_centering(result)
            print(f"Centering visualization created: {centering_vis.shape}")
            
            # Test original detection visualization
            original_vis = centerer.visualize_original_detection(test_image, best_detection, result)
            print(f"Original detection visualization created: {original_vis.shape}")
            
            # Save test images for manual inspection
            cv2.imwrite("test_original.jpg", test_image)
            cv2.imwrite("test_detection_vis.jpg", detection_vis)
            cv2.imwrite("test_centered.jpg", result.cropped_image)
            cv2.imwrite("test_centering_vis.jpg", centering_vis)
            cv2.imwrite("test_original_vis.jpg", original_vis)
            
            print("\nTest images saved:")
            print("- test_original.jpg: Original test image")
            print("- test_detection_vis.jpg: Detection visualization")
            print("- test_centered.jpg: Centered/cropped result")
            print("- test_centering_vis.jpg: Centering visualization")
            print("- test_original_vis.jpg: Original with detection and crop overlay")
            
            return True
        else:
            print("No humans detected in test image")
            # Save the test image anyway for inspection
            cv2.imwrite("test_original_no_detection.jpg", test_image)
            print("Saved test_original_no_detection.jpg for inspection")
            return False
            
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_centering_logic()
    if success:
        print("\n✓ Centering improvements test completed successfully!")
    else:
        print("\n✗ Centering improvements test failed!")
    sys.exit(0 if success else 1)
