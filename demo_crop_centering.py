#!/usr/bin/env python3
"""
Demonstration script for the new crop centering and padded preview functionality.
Creates realistic test images and shows the difference between traditional and crop centering.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def create_realistic_test_image(subject_position="left"):
    """Create a more realistic test image with a person-like figure."""
    # Create a 1200x800 landscape image
    image = np.zeros((800, 1200, 3), dtype=np.uint8)
    
    # Add a gradient background (sky to ground)
    for y in range(800):
        sky_blue = int(200 - (y / 800) * 100)
        ground_green = int(50 + (y / 800) * 100)
        image[y, :] = [ground_green, 150, sky_blue]
    
    # Add some background elements
    # Trees on the sides
    cv2.rectangle(image, (50, 300), (100, 800), (20, 80, 20), -1)  # Left tree
    cv2.rectangle(image, (1100, 250), (1150, 800), (20, 80, 20), -1)  # Right tree
    
    # Position the subject based on parameter
    if subject_position == "left":
        center_x = 300  # Left third
    elif subject_position == "right":
        center_x = 900  # Right third
    else:  # center
        center_x = 600  # Center
    
    center_y = 400  # Vertical center
    
    # Create a person-like figure
    # Head
    cv2.circle(image, (center_x, center_y - 120), 45, (220, 180, 160), -1)
    
    # Face features
    cv2.circle(image, (center_x - 15, center_y - 130), 5, (50, 50, 50), -1)  # Left eye
    cv2.circle(image, (center_x + 15, center_y - 130), 5, (50, 50, 50), -1)  # Right eye
    cv2.circle(image, (center_x, center_y - 110), 3, (100, 50, 50), -1)  # Nose
    
    # Body (torso)
    cv2.rectangle(image, (center_x - 40, center_y - 75), (center_x + 40, center_y + 100), (100, 150, 200), -1)
    
    # Arms
    cv2.rectangle(image, (center_x - 80, center_y - 60), (center_x - 40, center_y + 20), (100, 150, 200), -1)  # Left arm
    cv2.rectangle(image, (center_x + 40, center_y - 60), (center_x + 80, center_y + 20), (100, 150, 200), -1)  # Right arm
    
    # Hands
    cv2.circle(image, (center_x - 80, center_y + 20), 12, (220, 180, 160), -1)  # Left hand
    cv2.circle(image, (center_x + 80, center_y + 20), 12, (220, 180, 160), -1)  # Right hand
    
    # Legs
    cv2.rectangle(image, (center_x - 25, center_y + 100), (center_x - 5, center_y + 250), (80, 120, 180), -1)  # Left leg
    cv2.rectangle(image, (center_x + 5, center_y + 100), (center_x + 25, center_y + 250), (80, 120, 180), -1)  # Right leg
    
    # Feet
    cv2.ellipse(image, (center_x - 15, center_y + 250), (20, 8), 0, 0, 360, (50, 50, 50), -1)  # Left foot
    cv2.ellipse(image, (center_x + 15, center_y + 250), (20, 8), 0, 0, 360, (50, 50, 50), -1)  # Right foot
    
    return image, center_x, center_y

def create_detection_data(center_x, center_y):
    """Create realistic detection data for the test figure."""
    # Bounding box around the entire figure
    bbox = [center_x - 100, center_y - 170, center_x + 100, center_y + 270]
    
    # Keypoints for the figure
    keypoints = {
        'nose': (center_x, center_y - 110),
        'left_eye': (center_x - 15, center_y - 130),
        'right_eye': (center_x + 15, center_y - 130),
        'left_shoulder': (center_x - 40, center_y - 60),
        'right_shoulder': (center_x + 40, center_y - 60),
        'left_hip': (center_x - 25, center_y + 100),
        'right_hip': (center_x + 25, center_y + 100),
    }
    
    detection = {
        'bbox': bbox,
        'confidence': 0.95,
        'keypoints': keypoints,
        'center': (center_x, center_y)
    }
    
    return detection

def demonstrate_centering_comparison():
    """Demonstrate the difference between traditional and crop centering."""
    print("Creating demonstration images...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.image_processing.centering import PhotoCenterer
        from src.photo_center.image_processing.crop_centering import CropCenterer
        from src.photo_center.image_processing.padded_preview import PaddedPreviewGenerator
        
        # Initialize components
        config = Config()
        photo_centerer = PhotoCenterer(config)
        crop_centerer = CropCenterer(config)
        preview_generator = PaddedPreviewGenerator()
        
        output_dir = Path("demo_output")
        output_dir.mkdir(exist_ok=True)
        
        # Test with different subject positions
        positions = ["left", "center", "right"]
        
        for position in positions:
            print(f"\nProcessing {position} positioned subject...")
            
            # Create test image
            image, subject_x, subject_y = create_realistic_test_image(position)
            detection = create_detection_data(subject_x, subject_y)
            
            print(f"  Original image: {image.shape[1]}x{image.shape[0]}")
            print(f"  Subject position: ({subject_x}, {subject_y})")
            
            # Traditional centering
            traditional_result = photo_centerer.center_subject(image, detection)
            print(f"  Traditional centering confidence: {traditional_result.confidence:.3f}")
            
            # Crop centering
            crop_result = crop_centerer.center_subject_with_crop(
                image, detection, centering_method='face_chest_based'
            )
            print(f"  Crop centering confidence: {crop_result.confidence:.3f}")
            print(f"  Crop ratio: {crop_result.crop_ratio:.3f}")
            
            # Calculate centering accuracy
            trad_width = traditional_result.cropped_image.shape[1]
            crop_width = crop_result.cropped_image.shape[1]
            
            # Find where subject ended up in each result
            trad_crop_x1, trad_crop_y1, trad_crop_x2, trad_crop_y2 = traditional_result.crop_box
            crop_crop_x1, crop_crop_y1, crop_crop_x2, crop_crop_y2 = crop_result.crop_box
            
            trad_subject_pos = subject_x - trad_crop_x1
            crop_subject_pos = subject_x - crop_crop_x1
            
            trad_center_error = abs(trad_subject_pos - trad_width // 2)
            crop_center_error = abs(crop_subject_pos - crop_width // 2)
            
            print(f"  Traditional centering error: {trad_center_error} pixels")
            print(f"  Crop centering error: {crop_center_error} pixels")
            
            # Save original image
            cv2.imwrite(str(output_dir / f"{position}_original.jpg"), image)
            
            # Save traditional result
            cv2.imwrite(str(output_dir / f"{position}_traditional.jpg"), traditional_result.cropped_image)
            
            # Save crop result
            cv2.imwrite(str(output_dir / f"{position}_cropped.jpg"), crop_result.cropped_image)
            
            # Create padded preview
            orig_height, orig_width = image.shape[:2]
            padded_result = preview_generator.create_padded_preview(
                crop_result, (orig_width, orig_height), maintain_aspect=True
            )
            cv2.imwrite(str(output_dir / f"{position}_padded.jpg"), padded_result.preview_image)
            
            # Create overlay preview
            overlay_image = preview_generator.create_overlay_preview(image, crop_result)
            cv2.imwrite(str(output_dir / f"{position}_overlay.jpg"), overlay_image)
            
            # Create side-by-side comparison
            side_by_side = preview_generator.create_side_by_side_preview(image, crop_result)
            cv2.imwrite(str(output_dir / f"{position}_comparison.jpg"), side_by_side)
        
        print(f"\n✓ Demonstration completed!")
        print(f"Demo images saved to: {output_dir.absolute()}")
        print("\nGenerated files for each position (left, center, right):")
        print("  - *_original.jpg: Original image with subject")
        print("  - *_traditional.jpg: Traditional centering result")
        print("  - *_cropped.jpg: New crop centering result")
        print("  - *_padded.jpg: Crop result with magenta padding")
        print("  - *_overlay.jpg: Original with crop area highlighted")
        print("  - *_comparison.jpg: Side-by-side comparison")
        
        return True
        
    except Exception as e:
        print(f"✗ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_summary_comparison():
    """Create a summary image showing all results."""
    print("\nCreating summary comparison...")
    
    try:
        output_dir = Path("demo_output")
        
        # Load the left position images for summary
        original = cv2.imread(str(output_dir / "left_original.jpg"))
        traditional = cv2.imread(str(output_dir / "left_traditional.jpg"))
        cropped = cv2.imread(str(output_dir / "left_cropped.jpg"))
        padded = cv2.imread(str(output_dir / "left_padded.jpg"))
        
        if all(img is not None for img in [original, traditional, cropped, padded]):
            # Resize all images to same height for comparison
            target_height = 400
            
            def resize_to_height(img, height):
                h, w = img.shape[:2]
                scale = height / h
                new_width = int(w * scale)
                return cv2.resize(img, (new_width, height))
            
            original_resized = resize_to_height(original, target_height)
            traditional_resized = resize_to_height(traditional, target_height)
            cropped_resized = resize_to_height(cropped, target_height)
            padded_resized = resize_to_height(padded, target_height)
            
            # Create combined image
            padding = 20
            total_width = (original_resized.shape[1] + traditional_resized.shape[1] + 
                          cropped_resized.shape[1] + padded_resized.shape[1] + 3 * padding)
            
            combined = np.full((target_height + 60, total_width, 3), (255, 255, 255), dtype=np.uint8)
            
            # Place images
            x_offset = 0
            combined[40:40+target_height, x_offset:x_offset+original_resized.shape[1]] = original_resized
            
            x_offset += original_resized.shape[1] + padding
            combined[40:40+target_height, x_offset:x_offset+traditional_resized.shape[1]] = traditional_resized
            
            x_offset += traditional_resized.shape[1] + padding
            combined[40:40+target_height, x_offset:x_offset+cropped_resized.shape[1]] = cropped_resized
            
            x_offset += cropped_resized.shape[1] + padding
            combined[40:40+target_height, x_offset:x_offset+padded_resized.shape[1]] = padded_resized
            
            # Add labels
            labels = ["Original", "Traditional Centering", "Crop Centering", "Padded Preview"]
            x_positions = [original_resized.shape[1]//2, 
                          original_resized.shape[1] + padding + traditional_resized.shape[1]//2,
                          original_resized.shape[1] + padding + traditional_resized.shape[1] + padding + cropped_resized.shape[1]//2,
                          original_resized.shape[1] + padding + traditional_resized.shape[1] + padding + cropped_resized.shape[1] + padding + padded_resized.shape[1]//2]
            
            for i, (label, x_pos) in enumerate(zip(labels, x_positions)):
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                text_x = x_pos - text_size[0] // 2
                cv2.putText(combined, label, (text_x, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            
            cv2.imwrite(str(output_dir / "summary_comparison.jpg"), combined)
            print(f"✓ Summary comparison saved: {output_dir / 'summary_comparison.jpg'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Summary creation failed: {e}")
        return False

def main():
    """Run the demonstration."""
    print("Crop Centering and Padded Preview Demonstration")
    print("=" * 60)
    
    success = demonstrate_centering_comparison()
    if success:
        create_summary_comparison()
        
        print("\n" + "=" * 60)
        print("Demonstration Summary:")
        print("✓ Created realistic test images with subjects at different positions")
        print("✓ Compared traditional centering vs. crop centering")
        print("✓ Generated padded previews with magenta fill")
        print("✓ Created overlay and comparison views")
        print("\nKey Benefits of Crop Centering:")
        print("- Perfect left-to-right centering of subjects")
        print("- Better composition for portrait photos")
        print("- Magenta padding maintains original dimensions")
        print("- Multiple preview modes for different use cases")
        print("\nCheck the 'demo_output' directory to see all generated images!")
        return 0
    else:
        print("\n✗ Demonstration failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
