#!/usr/bin/env python3
"""
Test script for GUI integration of crop centering functionality.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_gui_components():
    """Test that GUI components can be imported and initialized."""
    print("Testing GUI component imports...")
    
    try:
        # Test core imports
        from src.photo_center.utils.config import Config
        from src.photo_center.models.human_detector import HumanDetector
        from src.photo_center.image_processing.raw_processor import RawProcessor
        from src.photo_center.image_processing.centering import PhotoCenterer
        from src.photo_center.image_processing.crop_centering import CropCenterer
        from src.photo_center.image_processing.padded_preview import PaddedPreviewGenerator
        
        print("✓ Core components imported successfully")
        
        # Test component initialization
        config = Config()
        human_detector = HumanDetector(config)
        raw_processor = RawProcessor(config)
        photo_centerer = PhotoCenterer(config)
        crop_centerer = CropCenterer(config)
        preview_generator = PaddedPreviewGenerator()
        
        print("✓ Components initialized successfully")
        
        # Test GUI imports (optional)
        try:
            from src.photo_center.ui.preview_widget import PreviewWidget
            from src.photo_center.ui.main_window import MainWindow
            print("✓ GUI components imported successfully")
            gui_available = True
        except ImportError as e:
            print(f"⚠ GUI components not available: {e}")
            gui_available = False
        
        return True, gui_available
        
    except Exception as e:
        print(f"✗ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, False

def test_processing_workflow():
    """Test the complete processing workflow."""
    print("\nTesting processing workflow...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.models.human_detector import HumanDetector
        from src.photo_center.image_processing.raw_processor import RawProcessor
        from src.photo_center.image_processing.centering import PhotoCenterer
        from src.photo_center.image_processing.crop_centering import CropCenterer
        from src.photo_center.image_processing.padded_preview import PaddedPreviewGenerator
        
        # Initialize components
        config = Config()
        human_detector = HumanDetector(config)
        raw_processor = RawProcessor(config)
        photo_centerer = PhotoCenterer(config)
        crop_centerer = CropCenterer(config)
        preview_generator = PaddedPreviewGenerator()
        
        # Use existing test image
        test_image_path = Path("test_output/original_test.jpg")
        if not test_image_path.exists():
            print("⚠ Test image not found, creating new one...")
            # Create a simple test image
            test_image = np.zeros((600, 800, 3), dtype=np.uint8)
            test_image[:] = [100, 150, 200]
            cv2.circle(test_image, (300, 200), 50, (255, 255, 255), -1)
            cv2.rectangle(test_image, (270, 250), (330, 400), (200, 200, 200), -1)
            cv2.imwrite(str(test_image_path), test_image)
        
        # Load image
        image = raw_processor.load_image(str(test_image_path))
        print(f"✓ Loaded test image: {image.shape}")
        
        # Detect humans
        detections = human_detector.detect_humans(image)
        print(f"✓ Human detection completed: {len(detections)} detections")
        
        if detections:
            # Get best detection
            best_detection = human_detector.get_best_detection(detections)
            print(f"✓ Best detection confidence: {best_detection['confidence']:.3f}")
            
            # Traditional centering
            centering_result = photo_centerer.center_subject(image, best_detection)
            print(f"✓ Traditional centering: confidence={centering_result.confidence:.3f}")
            
            # Crop centering
            crop_result = crop_centerer.center_subject_with_crop(
                image, best_detection, centering_method='face_chest_based'
            )
            print(f"✓ Crop centering: confidence={crop_result.confidence:.3f}, ratio={crop_result.crop_ratio:.3f}")
            
            # Generate previews
            orig_height, orig_width = image.shape[:2]
            
            # Padded preview
            padded_result = preview_generator.create_padded_preview(
                crop_result, (orig_width, orig_height), maintain_aspect=True
            )
            print(f"✓ Padded preview: scale={padded_result.scale_factor:.3f}")
            
            # Overlay preview
            overlay_image = preview_generator.create_overlay_preview(image, crop_result)
            print(f"✓ Overlay preview: {overlay_image.shape}")
            
            # Side-by-side preview
            side_by_side = preview_generator.create_side_by_side_preview(image, crop_result)
            print(f"✓ Side-by-side preview: {side_by_side.shape}")
            
            # Save test results
            output_dir = Path("test_output")
            cv2.imwrite(str(output_dir / "workflow_padded.jpg"), padded_result.preview_image)
            cv2.imwrite(str(output_dir / "workflow_overlay.jpg"), overlay_image)
            cv2.imwrite(str(output_dir / "workflow_comparison.jpg"), side_by_side)
            
            print("✓ Test images saved to test_output/")
            
        else:
            print("⚠ No humans detected in test image")
        
        return True
        
    except Exception as e:
        print(f"✗ Processing workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_preview_widget():
    """Test preview widget functionality."""
    print("\nTesting preview widget...")
    
    try:
        # Check if GUI is available
        try:
            from PySide6.QtWidgets import QApplication
            from src.photo_center.ui.preview_widget import PreviewWidget
            gui_available = True
        except ImportError:
            print("⚠ GUI not available, skipping preview widget test")
            return True
        
        # Create minimal Qt application for testing
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create preview widget
        preview_widget = PreviewWidget()
        print("✓ Preview widget created")
        
        # Test with dummy data
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image[:] = [100, 150, 200]
        
        preview_widget.set_original_image(test_image)
        print("✓ Original image set")
        
        # Test crop result setting
        from src.photo_center.image_processing.crop_centering import CropCenteringResult
        
        dummy_crop_result = CropCenteringResult(
            cropped_image=test_image[50:350, 100:500],
            crop_box=(100, 50, 500, 350),
            subject_center=(300, 200),
            target_center=(200, 150),
            confidence=0.85,
            method_used="test_method",
            crop_ratio=0.6,
            padding_needed=(100, 50)
        )
        
        preview_widget.set_crop_result(dummy_crop_result)
        print("✓ Crop result set")
        
        # Check that buttons are enabled
        assert preview_widget.show_crop_btn.isEnabled(), "Crop preview button should be enabled"
        assert preview_widget.show_padded_btn.isEnabled(), "Padded preview button should be enabled"
        print("✓ Preview buttons enabled correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Preview widget test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all integration tests."""
    print("GUI Integration Test for Crop Centering")
    print("=" * 50)
    
    tests = [
        test_gui_components,
        test_processing_workflow,
        test_preview_widget,
    ]
    
    results = []
    gui_available = False
    
    for test in tests:
        try:
            if test == test_gui_components:
                result, gui_available = test()
            else:
                result = test()
            results.append(result)
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user")
            break
        except Exception as e:
            print(f"\n✗ Unexpected error in {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed!")
        if gui_available:
            print("\nGUI integration is working correctly!")
            print("You can now run 'uv run python main.py' to test the full application.")
        else:
            print("\nCore functionality is working, but GUI is not available.")
            print("Install PySide6 to enable GUI mode.")
        print("\nNew features available:")
        print("- Crop centering for perfect left-right positioning")
        print("- Padded preview with magenta fill")
        print("- Overlay preview showing crop area")
        print("- Side-by-side comparison views")
        return 0
    else:
        print(f"✗ {total - passed} out of {total} tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
