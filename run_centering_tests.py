#!/usr/bin/env python3
"""
Simple test runner for centering functionality without pytest.
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

from src.photo_center.image_processing.centering import PhotoCenterer, CenteringResult
from src.photo_center.utils.config import Config

def create_test_image():
    """Create a test image."""
    # Create a 400x300 test image
    image = np.zeros((300, 400, 3), dtype=np.uint8)
    # Add some content
    cv2.rectangle(image, (150, 100), (250, 200), (255, 255, 255), -1)
    return image

def create_mock_detection_bbox():
    """Create a mock detection with bounding box."""
    return {
        'bbox': [150, 100, 250, 200],  # x1, y1, x2, y2
        'confidence': 0.85,
        'center': (200, 150),
        'keypoints': None
    }

def create_mock_detection_keypoints():
    """Create a mock detection with keypoints."""
    return {
        'bbox': [150, 100, 250, 200],
        'confidence': 0.90,
        'center': (200, 150),
        'keypoints': {
            'nose': (200, 120),
            'left_eye': (190, 115),
            'right_eye': (210, 115),
            'left_shoulder': (180, 140),
            'right_shoulder': (220, 140),
            'left_hip': (185, 180),
            'right_hip': (215, 180)
        }
    }

def test_center_by_bbox():
    """Test centering using bounding box method."""
    print("Testing bbox centering...")
    centerer = PhotoCenterer()
    test_image = create_test_image()
    mock_detection = create_mock_detection_bbox()
    
    result = centerer._center_by_bbox(test_image, mock_detection)
    
    assert isinstance(result, CenteringResult)
    assert result.method_used == 'bbox_based'
    assert 0.0 <= result.confidence <= 1.0
    assert result.cropped_image.shape[2] == 3  # RGB channels
    assert len(result.crop_box) == 4  # x1, y1, x2, y2
    assert len(result.subject_center) == 2  # x, y
    assert len(result.target_center) == 2  # x, y
    print("✓ Bbox centering test passed")

def test_center_by_keypoints():
    """Test centering using keypoints method."""
    print("Testing keypoint centering...")
    centerer = PhotoCenterer()
    test_image = create_test_image()
    mock_detection = create_mock_detection_keypoints()
    
    result = centerer._center_by_keypoints(test_image, mock_detection)
    
    assert isinstance(result, CenteringResult)
    assert result.method_used == 'keypoint_based'
    assert 0.0 <= result.confidence <= 1.0
    assert result.cropped_image.shape[2] == 3
    print("✓ Keypoint centering test passed")

def test_face_chest_centering():
    """Test the new face_chest_based centering method."""
    print("Testing face_chest centering...")
    centerer = PhotoCenterer()
    test_image = create_test_image()
    mock_detection = create_mock_detection_keypoints()
    
    result = centerer._center_by_face_chest(test_image, mock_detection)
    
    assert isinstance(result, CenteringResult)
    assert result.method_used == 'face_chest_based'
    assert 0.0 <= result.confidence <= 1.0
    assert result.cropped_image.shape[2] == 3
    print("✓ Face_chest centering test passed")

def test_visualizations():
    """Test the improved visualization methods."""
    print("Testing visualizations...")
    centerer = PhotoCenterer()
    test_image = create_test_image()
    mock_detection = create_mock_detection_keypoints()
    
    result = centerer.center_subject(test_image, mock_detection)
    
    # Test centering visualization
    vis_image = centerer.visualize_centering(result)
    assert vis_image.shape == result.cropped_image.shape
    assert vis_image.dtype == result.cropped_image.dtype
    # Visualization should be different from original (has overlays)
    assert not np.array_equal(vis_image, result.cropped_image)
    
    # Test original detection visualization
    original_vis = centerer.visualize_original_detection(test_image, mock_detection, result)
    assert original_vis.shape == test_image.shape
    assert original_vis.dtype == test_image.dtype
    # Should be different from original (has overlays)
    assert not np.array_equal(original_vis, test_image)
    
    print("✓ Visualization tests passed")

def test_crop_box_bounds():
    """Test that crop box stays within image bounds."""
    print("Testing crop box bounds...")
    centerer = PhotoCenterer()
    test_image = create_test_image()
    mock_detection = create_mock_detection_bbox()
    
    result = centerer.center_subject(test_image, mock_detection)
    
    x1, y1, x2, y2 = result.crop_box
    height, width = test_image.shape[:2]
    
    assert x1 >= 0
    assert y1 >= 0
    assert x2 <= width
    assert y2 <= height
    assert x2 > x1
    assert y2 > y1
    print("✓ Crop box bounds test passed")

def test_target_position():
    """Test that target position is correctly applied."""
    print("Testing target position...")
    centerer = PhotoCenterer()
    test_image = create_test_image()
    mock_detection = create_mock_detection_bbox()
    
    # Get config target position
    config = Config()
    target_pos = config.target_position
    print(f"Target position: {target_pos}")
    
    result = centerer.center_subject(test_image, mock_detection)
    
    # Check that target center matches expected position
    expected_x = int(result.cropped_image.shape[1] * target_pos[0])
    expected_y = int(result.cropped_image.shape[0] * target_pos[1])
    
    actual_x, actual_y = result.target_center
    
    print(f"Expected target: ({expected_x}, {expected_y})")
    print(f"Actual target: ({actual_x}, {actual_y})")
    
    # Allow reasonable tolerance for rounding and margin adjustments
    assert abs(actual_x - expected_x) <= 10
    assert abs(actual_y - expected_y) <= 10
    print("✓ Target position test passed")

def run_all_tests():
    """Run all tests."""
    print("Running centering tests...")
    print("=" * 50)
    
    tests = [
        test_center_by_bbox,
        test_center_by_keypoints,
        test_face_chest_centering,
        test_visualizations,
        test_crop_box_bounds,
        test_target_position,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed: {e}")
            failed += 1
    
    print("=" * 50)
    print(f"Tests completed: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✓ All tests passed!")
        return True
    else:
        print("✗ Some tests failed!")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
